"""
IXBrowser API调用处理模块

负责与IXBrowser进行HTTP通信，包括：
- 获取浏览器配置文件列表
- 错误处理和重试机制
- 连接状态管理
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIHandler:
    """IXBrowser API处理器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:53200"):
        """
        初始化API处理器
        
        Args:
            base_url: IXBrowser API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.profile_list_endpoint = f"{self.base_url}/api/v2/profile-list"
        self.session = requests.Session()
        self.session.timeout = 10  # 10秒超时
        
        # 缓存相关
        self._cached_profiles = []
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5分钟缓存
        
    def get_profiles(self, use_cache: bool = True) -> Tuple[List[Dict[str, Any]], bool]:
        """
        获取浏览器配置文件列表
        
        Args:
            use_cache: 是否使用缓存数据
            
        Returns:
            (profiles_list, is_success) 元组
            - profiles_list: 浏览器配置文件列表
            - is_success: API调用是否成功
        """
        # 检查缓存
        if use_cache and self._is_cache_valid():
            logger.info("使用缓存的浏览器列表数据")
            return self._cached_profiles, True
        
        try:
            logger.info(f"正在调用IXBrowser API: {self.profile_list_endpoint}")
            
            # 发送POST请求
            response = self.session.post(
                self.profile_list_endpoint,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'IXBrowser-GUI-Tool/1.0'
                },
                json={}  # 空的JSON body
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应数据
            data = response.json()
            logger.debug(f"API响应数据: {data}")

            # 验证响应格式
            if not isinstance(data, dict):
                raise ValueError("API响应格式错误：期望JSON对象")
            
            # 提取profiles列表（根据IXBrowser API响应格式）
            # 响应格式: {"error": {...}, "data": {"total": 248, "data": [...]}}
            profiles = []

            # 检查错误状态
            error_info = data.get('error', {})
            if error_info.get('code', -1) != 0:
                error_msg = error_info.get('message', '未知错误')
                raise ValueError(f"API返回错误: {error_msg}")

            # 提取数据部分
            data_section = data.get('data', {})
            if isinstance(data_section, dict):
                profiles = data_section.get('data', [])
                total_count = data_section.get('total', 0)
                logger.info(f"API返回总数: {total_count}")
            elif isinstance(data_section, list):
                # 兼容性处理：如果data直接是列表
                profiles = data_section

            # 确保profiles是列表
            if not isinstance(profiles, list):
                profiles = []
            
            # 更新缓存
            self._cached_profiles = profiles
            self._cache_timestamp = time.time()
            
            logger.info(f"成功获取 {len(profiles)} 个浏览器配置文件")
            return profiles, True
            
        except requests.exceptions.ConnectionError:
            logger.error("无法连接到IXBrowser API，请确保IXBrowser正在运行")
            return self._get_fallback_data(), False
            
        except requests.exceptions.Timeout:
            logger.error("API请求超时")
            return self._get_fallback_data(), False
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP错误: {e}")
            return self._get_fallback_data(), False
            
        except json.JSONDecodeError:
            logger.error("API响应不是有效的JSON格式")
            return self._get_fallback_data(), False
            
        except Exception as e:
            logger.error(f"获取浏览器列表时发生未知错误: {e}")
            return self._get_fallback_data(), False
    
    def test_connection(self) -> bool:
        """
        测试与IXBrowser的连接
        
        Returns:
            连接是否成功
        """
        try:
            response = self.session.get(
                f"{self.base_url}/api/v2/health",  # 假设有健康检查端点
                timeout=5
            )
            return response.status_code == 200
        except:
            # 如果没有健康检查端点，尝试profile-list
            try:
                response = self.session.post(
                    self.profile_list_endpoint,
                    json={},
                    timeout=5
                )
                return response.status_code in [200, 400, 404]  # 任何有效响应都表示连接正常
            except:
                return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息
        
        Returns:
            连接状态字典
        """
        is_connected = self.test_connection()
        
        return {
            "connected": is_connected,
            "api_url": self.profile_list_endpoint,
            "cache_valid": self._is_cache_valid(),
            "cached_profiles_count": len(self._cached_profiles),
            "last_cache_time": self._cache_timestamp
        }
    
    def refresh_profiles(self) -> Tuple[List[Dict[str, Any]], bool]:
        """
        强制刷新浏览器列表（忽略缓存）
        
        Returns:
            (profiles_list, is_success) 元组
        """
        return self.get_profiles(use_cache=False)
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cached_profiles:
            return False
        
        current_time = time.time()
        return (current_time - self._cache_timestamp) < self._cache_duration
    
    def _get_fallback_data(self) -> List[Dict[str, Any]]:
        """获取备用数据（使用缓存或空列表）"""
        if self._cached_profiles:
            logger.info("API调用失败，使用缓存数据")
            return self._cached_profiles
        else:
            logger.warning("API调用失败且无缓存数据，返回空列表")
            return []
    
    def format_profile_for_display(self, profile: Dict[str, Any]) -> Dict[str, str]:
        """
        格式化浏览器配置文件用于界面显示
        
        Args:
            profile: 原始配置文件数据
            
        Returns:
            格式化后的显示数据
        """
        return {
            "profile_id": str(profile.get("profile_id", "")),
            "name": profile.get("name", "未命名"),
            "group_name": profile.get("group_name", "默认分组"),
            "status": profile.get("status", "未知"),
            "created_time": profile.get("created_time", "")
        }


# 测试代码
if __name__ == "__main__":
    # 简单测试
    api = APIHandler()
    
    # 测试连接
    print(f"连接状态: {api.get_connection_status()}")
    
    # 测试获取配置文件
    profiles, success = api.get_profiles()
    print(f"获取结果: 成功={success}, 数量={len(profiles)}")
    
    if profiles:
        # 显示第一个配置文件的格式化数据
        formatted = api.format_profile_for_display(profiles[0])
        print(f"格式化示例: {formatted}")
