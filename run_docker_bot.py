#!/usr/bin/env python3
"""
Docker网站访问机器人启动脚本

此脚本用于启动Docker网站访问机器人，通过命令行参数指定浏览器配置文件ID。

使用方法:
    python run_docker_bot.py <profile_id>

参数:
    profile_id: IXBrowser配置文件ID（必需）

示例:
    python run_docker_bot.py 1268

作者: AI Assistant
版本: 2.0.0
创建日期: 2025-07-16
"""

import sys
import asyncio
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入模块
try:
    from bots.docker_bot import DockerBot
    from app.browser_manager import BrowserManager, ConfigManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保相关模块文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('docker_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='启动Docker网站访问机器人',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s 1268                    # 使用配置文件1268运行
        """
    )

    parser.add_argument(
        'profile_id',
        help='IXBrowser配置文件ID'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    return parser.parse_args()


async def main():
    """主函数"""
    print("=" * 60)
    print("Docker网站访问机器人 v2.0.0")
    print("=" * 60)

    try:
        # 解析命令行参数
        args = parse_arguments()

        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

        profile_id = args.profile_id

        # 验证profile_id
        if not profile_id:
            logger.error("配置文件ID不能为空")
            sys.exit(1)

        logger.info(f"配置文件ID: {profile_id}")

        # 创建管理器
        browser_manager = BrowserManager()
        config_manager = ConfigManager()

        # 启动浏览器
        logger.info("正在启动浏览器...")
        ws_endpoint = browser_manager.start_browser(profile_id)
        if not ws_endpoint:
            logger.error("启动浏览器失败")
            sys.exit(1)

        # 等待浏览器完全启动
        await asyncio.sleep(3)

        # 获取流程设置
        flow_settings = config_manager.load_flow_settings("docker_visit_v1")

        # 创建机器人实例
        logger.info("正在初始化Docker机器人...")
        bot = DockerBot(profile_id, ws_endpoint)

        # 初始化机器人
        if not await bot.init_browser():
            logger.error("初始化机器人失败")
            sys.exit(1)

        # 显示将要访问的网站列表
        sites = flow_settings.get('sites', [])
        if sites:
            logger.info(f"将访问 {len(sites)} 个网站:")
            for i, site in enumerate(sites, 1):
                logger.info(f"  {i}. {site.get('name', site.get('url', ''))}")

        # 启动机器人
        logger.info("启动Docker机器人...")
        stats = await bot.run(flow_settings)

        # 清理
        await bot.cleanup()

        # 显示最终状态
        logger.info("=" * 40)
        logger.info("运行完成统计:")
        logger.info(f"  配置文件ID: {profile_id}")
        logger.info(f"  访问网站数: {stats.get('sites_visited', 0)}")
        logger.info(f"  成功访问: {stats.get('successful_visits', 0)}")
        logger.info(f"  失败访问: {stats.get('failed_visits', 0)}")
        logger.info(f"  错误数: {stats.get('errors', 0)}")
        logger.info("=" * 40)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        if 'bot' in locals():
            try:
                await bot.cleanup()
            except:
                pass
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)

    logger.info("Docker机器人程序退出")


if __name__ == "__main__":
    asyncio.run(main())
