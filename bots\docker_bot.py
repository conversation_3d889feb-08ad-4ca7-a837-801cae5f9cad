"""
Docker网站访问机器人

负责访问指定的网站列表，模拟正常的浏览行为。

功能特点：
- 按顺序或随机访问网站列表
- 模拟真实用户浏览行为（滚动、停留、点击等）
- 可配置的访问间隔和停留时长
- 支持多种浏览模式
"""

import time
import random
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DockerBot:
    """Docker网站访问机器人"""
    
    def __init__(self, profile_id: str, config_file: str = "data/bot_configs/docker_sites.json"):
        """
        初始化Docker机器人
        
        Args:
            profile_id: 浏览器配置文件ID
            config_file: 配置文件路径
        """
        self.profile_id = profile_id
        self.config_file = Path(config_file)
        self.driver = None
        self.config = self._load_config()
        
        # 运行状态
        self.is_running = False
        self.sites_visited = 0
        self.start_time = None
        self.current_site = None
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        default_config = {
            "websites": [
                {
                    "url": "https://www.google.com",
                    "name": "Google搜索",
                    "stay_duration": {"min": 10, "max": 30},
                    "actions": ["scroll", "search"]
                },
                {
                    "url": "https://www.youtube.com",
                    "name": "YouTube",
                    "stay_duration": {"min": 20, "max": 60},
                    "actions": ["scroll", "click_video"]
                },
                {
                    "url": "https://www.amazon.com",
                    "name": "Amazon",
                    "stay_duration": {"min": 15, "max": 45},
                    "actions": ["scroll", "search_product"]
                },
                {
                    "url": "https://www.reddit.com",
                    "name": "Reddit",
                    "stay_duration": {"min": 30, "max": 90},
                    "actions": ["scroll", "click_post"]
                }
            ],
            "visit_mode": "random",  # "sequential" 或 "random"
            "visit_interval": {
                "min": 5,   # 访问间隔最小秒数
                "max": 15   # 访问间隔最大秒数
            },
            "browser_settings": {
                "page_load_timeout": 30,
                "implicit_wait": 10,
                "window_size": {"width": 1366, "height": 768}
            },
            "behavior_patterns": {
                "scroll_probability": 0.8,
                "click_probability": 0.3,
                "search_probability": 0.2,
                "back_probability": 0.1
            }
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并用户配置和默认配置
                    default_config.update(user_config)
                    logger.info(f"已加载配置文件: {self.config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                # 创建默认配置文件
                self._save_default_config(default_config)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
        
        return default_config
    
    def _save_default_config(self, config: Dict):
        """保存默认配置文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"已创建默认配置文件: {self.config_file}")
        except Exception as e:
            logger.error(f"保存默认配置文件失败: {e}")
    
    def start(self, duration_minutes: int = 60):
        """
        启动Docker机器人
        
        Args:
            duration_minutes: 运行时长（分钟）
        """
        logger.info(f"启动Docker网站访问机器人 - 配置文件ID: {self.profile_id}")
        
        try:
            # 初始化浏览器
            self._init_browser()
            
            # 设置运行状态
            self.is_running = True
            self.start_time = time.time()
            end_time = self.start_time + (duration_minutes * 60)
            
            # 获取网站列表
            websites = self.config["websites"]
            if not websites:
                logger.error("网站列表为空")
                return
            
            # 主循环
            while self.is_running and time.time() < end_time:
                try:
                    # 选择网站
                    site = self._select_next_site(websites)
                    
                    # 访问网站
                    self._visit_site(site)
                    
                    # 等待间隔
                    interval = random.randint(
                        self.config["visit_interval"]["min"],
                        self.config["visit_interval"]["max"]
                    )
                    logger.info(f"等待 {interval} 秒后访问下一个网站")
                    time.sleep(interval)
                    
                except Exception as e:
                    logger.error(f"执行循环时发生错误: {e}")
                    time.sleep(10)  # 错误后等待10秒
            
            logger.info(f"Docker机器人运行结束 - 共访问 {self.sites_visited} 个网站")
            
        except Exception as e:
            logger.error(f"Docker机器人运行失败: {e}")
        finally:
            self.stop()
    
    def _init_browser(self):
        """初始化浏览器"""
        # 这里需要根据IXBrowser的具体API来实现
        # 暂时使用标准的Chrome WebDriver作为示例
        
        options = webdriver.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 设置窗口大小
        window_size = self.config["browser_settings"]["window_size"]
        options.add_argument(f"--window-size={window_size['width']},{window_size['height']}")
        
        # 注意：实际使用时需要替换为IXBrowser的连接方式
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 设置超时
        self.driver.set_page_load_timeout(self.config["browser_settings"]["page_load_timeout"])
        self.driver.implicitly_wait(self.config["browser_settings"]["implicit_wait"])
        
        logger.info("浏览器初始化完成")
    
    def _select_next_site(self, websites: List[Dict]) -> Dict:
        """选择下一个要访问的网站"""
        if self.config["visit_mode"] == "random":
            return random.choice(websites)
        else:  # sequential
            index = self.sites_visited % len(websites)
            return websites[index]
    
    def _visit_site(self, site: Dict):
        """访问指定网站"""
        try:
            url = site["url"]
            name = site.get("name", url)
            self.current_site = name
            
            logger.info(f"正在访问: {name} ({url})")
            
            # 访问网站
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 获取停留时长
            stay_duration = random.randint(
                site["stay_duration"]["min"],
                site["stay_duration"]["max"]
            )
            
            logger.info(f"在 {name} 停留 {stay_duration} 秒")
            
            # 执行浏览行为
            self._perform_browsing_behavior(site, stay_duration)
            
            self.sites_visited += 1
            
        except TimeoutException:
            logger.warning(f"访问 {site.get('name', site['url'])} 超时")
        except WebDriverException as e:
            logger.error(f"访问 {site.get('name', site['url'])} 时发生WebDriver错误: {e}")
        except Exception as e:
            logger.error(f"访问网站失败: {e}")
    
    def _perform_browsing_behavior(self, site: Dict, duration: int):
        """执行浏览行为"""
        end_time = time.time() + duration
        actions = site.get("actions", ["scroll"])
        
        while time.time() < end_time and self.is_running:
            try:
                # 随机选择一个动作
                action = random.choice(actions)
                
                if action == "scroll" and random.random() < self.config["behavior_patterns"]["scroll_probability"]:
                    self._scroll_page()
                
                elif action == "search" and random.random() < self.config["behavior_patterns"]["search_probability"]:
                    self._try_search()
                
                elif action == "click_video" and random.random() < self.config["behavior_patterns"]["click_probability"]:
                    self._try_click_video()
                
                elif action == "click_post" and random.random() < self.config["behavior_patterns"]["click_probability"]:
                    self._try_click_post()
                
                elif action == "search_product" and random.random() < self.config["behavior_patterns"]["search_probability"]:
                    self._try_search_product()
                
                # 随机等待
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logger.debug(f"执行浏览行为时出错: {e}")
    
    def _scroll_page(self):
        """滚动页面"""
        try:
            # 随机滚动方向和距离
            scroll_distance = random.randint(200, 800)
            direction = random.choice([1, -1])  # 向下或向上
            
            self.driver.execute_script(f"window.scrollBy(0, {scroll_distance * direction});")
            logger.debug("执行页面滚动")
        except:
            pass
    
    def _try_search(self):
        """尝试搜索"""
        try:
            # 查找搜索框（通用选择器）
            search_selectors = [
                "input[name='q']",
                "input[type='search']",
                "#search",
                ".search-input",
                "input[placeholder*='搜索']",
                "input[placeholder*='Search']"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if search_box and search_box.is_displayed():
                # 随机搜索词
                search_terms = ["technology", "news", "weather", "sports", "music"]
                term = random.choice(search_terms)
                
                search_box.clear()
                search_box.send_keys(term)
                
                # 尝试提交搜索
                search_box.submit()
                logger.debug(f"执行搜索: {term}")
                
        except:
            pass
    
    def _try_click_video(self):
        """尝试点击视频（YouTube）"""
        try:
            # 查找视频链接
            video_selectors = [
                "a[href*='/watch']",
                ".ytd-video-renderer a",
                "#video-title"
            ]
            
            for selector in video_selectors:
                try:
                    videos = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if videos:
                        video = random.choice(videos[:5])  # 从前5个中选择
                        if video.is_displayed():
                            video.click()
                            logger.debug("点击了视频")
                            time.sleep(5)  # 观看5秒
                            self.driver.back()  # 返回
                            return
                except:
                    continue
        except:
            pass
    
    def _try_click_post(self):
        """尝试点击帖子（Reddit）"""
        try:
            # 查找帖子链接
            post_selectors = [
                "a[data-click-id='body']",
                ".Post a",
                "[data-testid='post-content'] a"
            ]
            
            for selector in post_selectors:
                try:
                    posts = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if posts:
                        post = random.choice(posts[:3])  # 从前3个中选择
                        if post.is_displayed():
                            post.click()
                            logger.debug("点击了帖子")
                            time.sleep(3)
                            self.driver.back()  # 返回
                            return
                except:
                    continue
        except:
            pass
    
    def _try_search_product(self):
        """尝试搜索商品（Amazon）"""
        try:
            # 查找搜索框
            search_box = self.driver.find_element(By.ID, "twotabsearchtextbox")
            if search_box.is_displayed():
                # 随机商品关键词
                products = ["laptop", "headphones", "book", "phone", "camera"]
                product = random.choice(products)
                
                search_box.clear()
                search_box.send_keys(product)
                
                # 点击搜索按钮
                search_btn = self.driver.find_element(By.ID, "nav-search-submit-button")
                search_btn.click()
                logger.debug(f"搜索商品: {product}")
                
        except:
            pass
    
    def stop(self):
        """停止机器人"""
        self.is_running = False
        
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器已关闭")
            except:
                pass
        
        logger.info("Docker机器人已停止")
    
    def get_status(self) -> Dict:
        """获取运行状态"""
        runtime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "is_running": self.is_running,
            "profile_id": self.profile_id,
            "sites_visited": self.sites_visited,
            "current_site": self.current_site,
            "runtime_seconds": int(runtime),
            "runtime_formatted": f"{int(runtime//60)}分{int(runtime%60)}秒"
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试机器人
    bot = DockerBot("test_profile")
    
    try:
        # 运行5分钟测试
        bot.start(duration_minutes=5)
    except KeyboardInterrupt:
        print("用户中断")
        bot.stop()
