#!/usr/bin/env python3
"""
YouTube机器人启动脚本

此脚本用于启动YouTube自动化机器人，通过命令行参数指定浏览器配置文件ID。

使用方法:
    python run_youtube_bot.py <profile_id>

参数:
    profile_id: IXBrowser配置文件ID（必需）

示例:
    python run_youtube_bot.py 1267

作者: AI Assistant
版本: 2.0.0
创建日期: 2025-07-16
"""

import sys
import asyncio
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入模块
try:
    from bots.youtube_bot import YouTubeBot
    from app.browser_manager import BrowserManager, ConfigManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保相关模块文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('youtube_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='启动YouTube自动化机器人',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s 1267                    # 使用配置文件1267运行
        """
    )

    parser.add_argument(
        'profile_id',
        help='IXBrowser配置文件ID'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    return parser.parse_args()


async def main():
    """主函数"""
    print("=" * 60)
    print("YouTube自动化机器人 v2.0.0")
    print("=" * 60)

    try:
        # 解析命令行参数
        args = parse_arguments()

        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

        profile_id = args.profile_id

        # 验证profile_id
        if not profile_id:
            logger.error("配置文件ID不能为空")
            sys.exit(1)

        logger.info(f"配置文件ID: {profile_id}")

        # 创建管理器
        browser_manager = BrowserManager()
        config_manager = ConfigManager()

        # 启动浏览器
        logger.info("正在启动浏览器...")
        ws_endpoint = browser_manager.start_browser(profile_id)
        if not ws_endpoint:
            logger.error("启动浏览器失败")
            sys.exit(1)

        # 等待浏览器完全启动
        await asyncio.sleep(3)

        # 获取流程设置
        flow_settings = config_manager.load_flow_settings("youtube_shorts_v1")

        # 创建机器人实例
        logger.info("正在初始化YouTube机器人...")
        bot = YouTubeBot(profile_id, ws_endpoint)

        # 初始化机器人
        if not await bot.init_browser():
            logger.error("初始化机器人失败")
            sys.exit(1)

        # 启动机器人
        logger.info("启动YouTube机器人...")
        stats = await bot.run(flow_settings)

        # 清理
        await bot.cleanup()

        # 显示最终状态
        logger.info("=" * 40)
        logger.info("运行完成统计:")
        logger.info(f"  配置文件ID: {profile_id}")
        logger.info(f"  观看视频数: {stats.get('videos_watched', 0)}")
        logger.info(f"  点赞数: {stats.get('likes_given', 0)}")
        logger.info(f"  订阅数: {stats.get('subscriptions_made', 0)}")
        logger.info(f"  错误数: {stats.get('errors', 0)}")
        logger.info("=" * 40)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        if 'bot' in locals():
            try:
                await bot.cleanup()
            except:
                pass
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)

    logger.info("YouTube机器人程序退出")


if __name__ == "__main__":
    asyncio.run(main())
