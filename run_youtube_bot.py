#!/usr/bin/env python3
"""
YouTube机器人启动脚本

此脚本用于启动YouTube自动化机器人，通过命令行参数指定浏览器配置文件ID。

使用方法:
    python run_youtube_bot.py <profile_id> [duration_minutes]

参数:
    profile_id: IXBrowser配置文件ID（必需）
    duration_minutes: 运行时长（分钟，可选，默认60分钟）

示例:
    python run_youtube_bot.py 1267
    python run_youtube_bot.py 1267 30

作者: AI Assistant
版本: 1.0.0
创建日期: 2025-07-16
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入机器人模块
try:
    from bots.youtube_bot import YouTubeBot
except ImportError as e:
    print(f"导入YouTubeBot失败: {e}")
    print("请确保bots/youtube_bot.py文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('youtube_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='启动YouTube自动化机器人',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s 1267                    # 使用配置文件1267运行60分钟
  %(prog)s 1267 --duration 30      # 使用配置文件1267运行30分钟
  %(prog)s 1267 -d 120             # 使用配置文件1267运行120分钟
        """
    )
    
    parser.add_argument(
        'profile_id',
        help='IXBrowser配置文件ID'
    )
    
    parser.add_argument(
        '-d', '--duration',
        type=int,
        default=60,
        help='运行时长（分钟，默认60分钟）'
    )
    
    parser.add_argument(
        '-c', '--config',
        default='data/bot_configs/youtube_xpath.json',
        help='配置文件路径（默认: data/bot_configs/youtube_xpath.json）'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    return parser.parse_args()


def validate_arguments(args):
    """验证参数有效性"""
    # 验证profile_id
    if not args.profile_id:
        logger.error("配置文件ID不能为空")
        return False
    
    # 验证duration
    if args.duration <= 0:
        logger.error("运行时长必须大于0")
        return False
    
    if args.duration > 1440:  # 24小时
        logger.warning("运行时长超过24小时，请确认这是预期的")
    
    # 验证配置文件路径
    config_path = Path(args.config)
    if not config_path.parent.exists():
        logger.info(f"配置文件目录不存在，将创建: {config_path.parent}")
        config_path.parent.mkdir(parents=True, exist_ok=True)
    
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("YouTube自动化机器人 v1.0.0")
    print("=" * 60)
    
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 验证参数
        if not validate_arguments(args):
            sys.exit(1)
        
        # 显示启动信息
        logger.info(f"配置文件ID: {args.profile_id}")
        logger.info(f"运行时长: {args.duration} 分钟")
        logger.info(f"配置文件: {args.config}")
        
        # 创建机器人实例
        logger.info("正在初始化YouTube机器人...")
        bot = YouTubeBot(
            profile_id=args.profile_id,
            config_file=args.config
        )
        
        # 启动机器人
        logger.info("启动YouTube机器人...")
        bot.start(duration_minutes=args.duration)
        
        # 显示最终状态
        status = bot.get_status()
        logger.info("=" * 40)
        logger.info("运行完成统计:")
        logger.info(f"  配置文件ID: {status['profile_id']}")
        logger.info(f"  观看视频数: {status['videos_watched']}")
        logger.info(f"  运行时长: {status['runtime_formatted']}")
        logger.info("=" * 40)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        if 'bot' in locals():
            bot.stop()
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)
    
    logger.info("YouTube机器人程序退出")


if __name__ == "__main__":
    main()
