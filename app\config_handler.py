"""
配置文件处理模块

负责读写data目录下的JSON配置文件，包括：
- flows.json: 自动化流程定义
- config.json: 用户配置映射
- 智能合并新旧配置数据
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfigHandler:
    """配置文件处理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化配置处理器
        
        Args:
            data_dir: 配置文件目录路径
        """
        self.data_dir = Path(data_dir)
        self.flows_file = self.data_dir / "flows.json"
        self.config_file = self.data_dir / "config.json"
        
        # 确保数据目录存在
        self.data_dir.mkdir(exist_ok=True)
        
    def load_flows(self) -> List[Dict[str, Any]]:
        """
        加载自动化流程定义
        
        Returns:
            流程定义列表
        """
        try:
            if self.flows_file.exists():
                with open(self.flows_file, 'r', encoding='utf-8') as f:
                    flows = json.load(f)
                logger.info(f"成功加载 {len(flows)} 个流程定义")
                return flows
            else:
                logger.warning("flows.json文件不存在，返回空列表")
                return []
        except Exception as e:
            logger.error(f"加载flows.json失败: {e}")
            return []
    
    def load_config(self) -> Dict[str, str]:
        """
        加载用户配置映射
        
        Returns:
            配置字典 {profile_id: flow_id}
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 过滤掉元数据，只返回实际配置
                config = {k: v for k, v in data.items() 
                         if not k.startswith('_')}
                
                logger.info(f"成功加载 {len(config)} 个配置项")
                return config
            else:
                logger.info("config.json文件不存在，返回空配置")
                return {}
        except Exception as e:
            logger.error(f"加载config.json失败: {e}")
            return {}
    
    def save_config(self, config: Dict[str, str]) -> bool:
        """
        保存用户配置映射
        
        Args:
            config: 配置字典 {profile_id: flow_id}
            
        Returns:
            保存是否成功
        """
        try:
            # 构建完整的配置数据，包含元数据
            full_data = {
                "_metadata": {
                    "version": "1.0",
                    "last_updated": self._get_current_timestamp(),
                    "description": "IXBrowser自动化流程配置文件",
                    "format": "profile_id -> flow_id 映射"
                }
            }
            
            # 添加实际配置数据
            full_data.update(config)
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(full_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存 {len(config)} 个配置项到 {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存config.json失败: {e}")
            return False
    
    def merge_configs(self, api_profiles: List[Dict], 
                     existing_config: Dict[str, str]) -> Dict[str, str]:
        """
        智能合并API数据与现有配置
        
        Args:
            api_profiles: 从API获取的浏览器配置文件列表
            existing_config: 现有的配置映射
            
        Returns:
            合并后的配置字典
        """
        merged_config = existing_config.copy()
        
        # 获取API中的所有profile_id
        api_profile_ids = {str(profile.get('profile_id', '')) 
                          for profile in api_profiles}
        
        # 为新的profile_id添加默认配置
        for profile_id in api_profile_ids:
            if profile_id and profile_id not in merged_config:
                merged_config[profile_id] = "none"  # 默认为"无"流程
                logger.info(f"为新浏览器 {profile_id} 添加默认配置")
        
        # 可选：清理不再存在的profile_id（保留历史配置）
        # removed_profiles = set(existing_config.keys()) - api_profile_ids
        # if removed_profiles:
        #     logger.info(f"检测到已删除的浏览器: {removed_profiles}")
        
        return merged_config
    
    def get_flow_by_id(self, flow_id: str) -> Optional[Dict[str, Any]]:
        """
        根据flow_id获取流程定义
        
        Args:
            flow_id: 流程ID
            
        Returns:
            流程定义字典，如果未找到返回None
        """
        flows = self.load_flows()
        for flow in flows:
            if flow.get('flow_id') == flow_id:
                return flow
        return None
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


# 测试代码
if __name__ == "__main__":
    # 简单测试
    handler = ConfigHandler()
    
    # 测试加载流程
    flows = handler.load_flows()
    print(f"加载的流程: {flows}")
    
    # 测试加载配置
    config = handler.load_config()
    print(f"加载的配置: {config}")
    
    # 测试保存配置
    test_config = {"1001": "youtube_shorts_v1", "1002": "none"}
    success = handler.save_config(test_config)
    print(f"保存结果: {success}")
