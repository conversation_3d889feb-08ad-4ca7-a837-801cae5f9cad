#!/usr/bin/env python3
"""
IXBrowser管理模块

负责与IXBrowser API交互，获取浏览器信息和WebSocket端点
"""

import requests
import json
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class BrowserManager:
    """IXBrowser管理器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:53200"):
        """
        初始化浏览器管理器
        
        Args:
            base_url: IXBrowser API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 10
        
    def get_browser_list(self) -> List[Dict]:
        """
        获取浏览器列表
        
        Returns:
            浏览器配置文件列表
        """
        try:
            url = f"{self.base_url}/api/v2/profile-list"
            response = self.session.post(
                url,
                headers={'Content-Type': 'application/json'},
                json={"page": 1, "limit": 1000}
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('error', {}).get('code') != 0:
                raise ValueError(f"API错误: {data.get('error', {}).get('message')}")
            
            profiles = data.get('data', {}).get('data', [])
            logger.info(f"获取到 {len(profiles)} 个浏览器配置文件")
            return profiles
            
        except Exception as e:
            logger.error(f"获取浏览器列表失败: {e}")
            return []
    
    def start_browser(self, profile_id: str) -> Optional[str]:
        """
        启动浏览器并获取WebSocket端点
        
        Args:
            profile_id: 浏览器配置文件ID
            
        Returns:
            WebSocket端点URL，失败返回None
        """
        try:
            url = f"{self.base_url}/api/v2/profile-start"
            response = self.session.post(
                url,
                headers={'Content-Type': 'application/json'},
                json={"profile_id": int(profile_id)}
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('error', {}).get('code') != 0:
                raise ValueError(f"启动浏览器失败: {data.get('error', {}).get('message')}")
            
            ws_endpoint = data.get('data', {}).get('ws_endpoint')
            if ws_endpoint:
                logger.info(f"浏览器 {profile_id} 启动成功，WebSocket: {ws_endpoint}")
                return ws_endpoint
            else:
                logger.error(f"浏览器 {profile_id} 启动失败：未获取到WebSocket端点")
                return None
                
        except Exception as e:
            logger.error(f"启动浏览器 {profile_id} 失败: {e}")
            return None
    
    def stop_browser(self, profile_id: str) -> bool:
        """
        停止浏览器
        
        Args:
            profile_id: 浏览器配置文件ID
            
        Returns:
            是否停止成功
        """
        try:
            url = f"{self.base_url}/api/v2/profile-stop"
            response = self.session.post(
                url,
                headers={'Content-Type': 'application/json'},
                json={"profile_id": int(profile_id)}
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('error', {}).get('code') != 0:
                logger.warning(f"停止浏览器 {profile_id} 可能失败: {data.get('error', {}).get('message')}")
                return False
            
            logger.info(f"浏览器 {profile_id} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止浏览器 {profile_id} 失败: {e}")
            return False
    
    def get_browser_status(self, profile_id: str) -> str:
        """
        获取浏览器状态
        
        Args:
            profile_id: 浏览器配置文件ID
            
        Returns:
            浏览器状态 ('running', 'stopped', 'unknown')
        """
        try:
            url = f"{self.base_url}/api/v2/profile-status"
            response = self.session.post(
                url,
                headers={'Content-Type': 'application/json'},
                json={"profile_id": int(profile_id)}
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('error', {}).get('code') != 0:
                return 'unknown'
            
            status = data.get('data', {}).get('status', 'unknown')
            return status
            
        except Exception as e:
            logger.error(f"获取浏览器 {profile_id} 状态失败: {e}")
            return 'unknown'


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "data/config.json", flows_file: str = "data/bot_flows.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: GUI配置文件路径
            flows_file: 流程配置文件路径
        """
        self.config_file = Path(config_file)
        self.flows_file = Path(flows_file)
    
    def load_browser_configs(self) -> Dict[str, str]:
        """
        加载浏览器配置映射
        
        Returns:
            {profile_id: flow_id} 映射字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 过滤掉元数据
                config = {k: v for k, v in data.items() if not k.startswith('_')}
                logger.info(f"加载了 {len(config)} 个浏览器配置")
                return config
            else:
                logger.warning("配置文件不存在")
                return {}
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def load_flow_settings(self, flow_id: str) -> Dict:
        """
        加载流程设置
        
        Args:
            flow_id: 流程ID
            
        Returns:
            流程设置字典
        """
        try:
            if self.flows_file.exists():
                with open(self.flows_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                flows = data.get('flows', {})
                for flow_key, flow_config in flows.items():
                    if flow_config.get('id') == flow_id:
                        return flow_config.get('settings', {})
                
                logger.warning(f"未找到流程 {flow_id} 的设置")
                return {}
            else:
                logger.warning("流程配置文件不存在")
                return {}
                
        except Exception as e:
            logger.error(f"加载流程设置失败: {e}")
            return {}
    
    def get_flow_type(self, flow_id: str) -> str:
        """
        获取流程类型
        
        Args:
            flow_id: 流程ID
            
        Returns:
            流程类型 ('youtube', 'docker', 'none')
        """
        try:
            if self.flows_file.exists():
                with open(self.flows_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                flows = data.get('flows', {})
                for flow_key, flow_config in flows.items():
                    if flow_config.get('id') == flow_id:
                        return flow_config.get('type', 'none')
                
                return 'none'
            else:
                return 'none'
                
        except Exception as e:
            logger.error(f"获取流程类型失败: {e}")
            return 'none'
