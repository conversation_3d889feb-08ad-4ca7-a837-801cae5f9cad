#!/usr/bin/env python3
"""
IXBrowser API测试脚本

用于测试和调试IXBrowser API调用
"""

import requests
import json
import sys

def test_api():
    """测试IXBrowser API"""
    url = "http://127.0.0.1:53200/api/v2/profile-list"
    
    print("=" * 50)
    print("IXBrowser API测试")
    print("=" * 50)
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(
            url,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'IXBrowser-GUI-Tool/1.0'
            },
            json={},  # 空的JSON body
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("\n响应数据结构:")
                print(f"类型: {type(data)}")
                
                if isinstance(data, dict):
                    print("顶级键:", list(data.keys()))
                    
                    # 检查error部分
                    if 'error' in data:
                        error = data['error']
                        print(f"错误信息: {error}")
                        
                        if error.get('code') == 0:
                            print("✓ API调用成功")
                        else:
                            print(f"✗ API返回错误: {error.get('message')}")
                    
                    # 检查data部分
                    if 'data' in data:
                        data_section = data['data']
                        print(f"数据部分类型: {type(data_section)}")
                        
                        if isinstance(data_section, dict):
                            print("数据部分键:", list(data_section.keys()))
                            
                            total = data_section.get('total', 0)
                            profiles = data_section.get('data', [])
                            
                            print(f"总数: {total}")
                            print(f"配置文件数量: {len(profiles) if isinstance(profiles, list) else '非列表类型'}")
                            
                            if isinstance(profiles, list) and profiles:
                                print("\n第一个配置文件示例:")
                                first_profile = profiles[0]
                                for key, value in first_profile.items():
                                    print(f"  {key}: {value}")
                            elif isinstance(profiles, list):
                                print("配置文件列表为空")
                            else:
                                print(f"配置文件数据类型错误: {type(profiles)}")
                
                print(f"\n完整响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
            except json.JSONDecodeError as e:
                print(f"✗ JSON解析失败: {e}")
                print(f"原始响应内容: {response.text}")
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 连接失败: 无法连接到IXBrowser API")
        print("请确保:")
        print("1. IXBrowser正在运行")
        print("2. API端口为53200")
        print("3. 没有防火墙阻止连接")
        
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
        
    except Exception as e:
        print(f"✗ 未知错误: {e}")

if __name__ == "__main__":
    test_api()
