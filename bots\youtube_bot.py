"""
YouTube Shorts 自动化机器人 - 完整版本

使用 Playwright 进行浏览器自动化，连接到IXBrowser实例
负责自动观看、点赞、订阅 YouTube Shorts 视频
"""

import asyncio
import random
import logging
import time
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Playwright, BrowserContext, TimeoutError

# 配置日志
logger = logging.getLogger(__name__)


class YouTubeBot:
    """YouTube Shorts 自动化机器人"""

    def __init__(self, browser_id: str, ws_endpoint: str):
        """
        初始化机器人

        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
        """
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.running = False

        # Playwright 对象
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        # 配置文件路径
        self.config_file = Path("data/bot_configs/xpath_config.json")
        self.xpath_config = self._load_xpath_config()

        # 统计数据
        self.stats = {
            "videos_watched": 0,
            "likes_given": 0,
            "subscriptions_made": 0,
            "errors": 0
        }
    
    def _load_xpath_config(self) -> Dict[str, Any]:
        """加载XPath配置文件"""
        default_config = {
            "selectors": {
                "like_button": [
                    "button[aria-label*='like' i]",
                    "button[aria-label*='赞' i]",
                    "#like-button",
                    "ytd-toggle-button-renderer:has(a[aria-label*='like' i])"
                ],
                "subscribe_button": [
                    "button[aria-label*='subscribe' i]",
                    "button[aria-label*='订阅' i]",
                    "#subscribe-button",
                    "ytd-subscribe-button-renderer button"
                ]
            }
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logger.info(f"已加载XPath配置文件: {self.config_file}")
            else:
                logger.info("XPath配置文件不存在，使用默认配置")
                # 创建默认配置文件
                self._save_default_xpath_config(default_config)
        except Exception as e:
            logger.error(f"加载XPath配置文件失败: {e}")
        
        return default_config
    
    def _save_default_xpath_config(self, config: Dict[str, Any]):
        """保存默认XPath配置文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"已创建默认XPath配置文件: {self.config_file}")
        except Exception as e:
            logger.error(f"保存默认XPath配置文件失败: {e}")
    
    async def random_delay(self, min_seconds: float, max_seconds: float):
        """随机延迟等待"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    async def init_browser(self) -> bool:
        """
        初始化浏览器连接

        Returns:
            bool: 是否初始化成功
        """
        try:
            logger.info(f"正在初始化YouTube机器人浏览器连接: {self.browser_id}")

            # 启动 Playwright
            self.playwright = await async_playwright().start()

            # 连接到现有浏览器实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                self.ws_endpoint,
                timeout=30000
            )

            # 获取已有的context
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                # 创建新的context，适合Shorts的设置
                self.context = await self.browser.new_context(
                    viewport={"width": 480, "height": 720},
                    user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
                )

            # 获取已有的页面，如果没有则创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]
                # 关闭其他页面
                for page in pages[1:]:
                    await page.close()
                logger.info(f"使用现有页面: {await self.page.title()}")
            else:
                self.page = await self.context.new_page()
                logger.info("创建新页面")

            # 设置页面超时时间
            self.page.set_default_timeout(20000)

            logger.info(f"YouTube机器人 {self.browser_id} 初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化YouTube机器人 {self.browser_id} 失败: {e}")
            await self.cleanup()
            return False

    async def check_connection(self) -> bool:
        """检查浏览器连接是否仍然有效"""
        try:
            await self.page.evaluate("() => document.location.href")
            return True
        except Exception:
            return False

    async def run(self, settings: Dict[str, Any]) -> Dict[str, int]:
        """运行机器人主循环"""
        self.running = True
        scroll_count = settings.get("scroll_count", 50)

        logger.info(f"开始运行YouTube机器人 {self.browser_id}，目标视频数: {scroll_count}")

        try:
            # 确保页面已加载完成
            await self.random_delay(2, 4)

            # 如果当前不是YouTube Shorts页面，则导航过去
            current_url = self.page.url
            if 'youtube.com/shorts' not in current_url:
                try:
                    await self.page.goto('https://www.youtube.com/shorts', timeout=30000)
                    await self.random_delay(3, 5)
                except Exception as e:
                    logger.warning(f"YouTube机器人 {self.browser_id} 导航到YouTube Shorts失败: {e}")
                    await self.page.reload()
                    await self.random_delay(3, 5)

            # 主循环
            for i in range(scroll_count):
                if not self.running:
                    break

                # 观看视频
                watch_time = random.randint(
                    settings.get("min_watch_time", 3),
                    settings.get("max_watch_time", 8)
                )
                await self.watch_video(watch_time)

                # 尝试点赞和订阅
                await self.try_like(settings.get("like_probability", 0.3))
                await self.try_subscribe(settings.get("subscribe_probability", 0.1))

                # 滚动到下一个视频
                if i < scroll_count - 1:
                    await self.scroll_to_next()

            logger.info(f"YouTube机器人 {self.browser_id} 任务完成")

        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 运行时发生错误: {e}")
            self.stats["errors"] += 1

        finally:
            self.running = False

        return self.stats.copy()
    
    async def watch_video(self, watch_time: int) -> bool:
        """观看当前视频"""
        try:
            await self.random_delay(watch_time * 0.8, watch_time * 1.2)
            self.stats["videos_watched"] += 1
            logger.info(f"YouTube机器人 {self.browser_id} 完成视频观看 #{self.stats['videos_watched']}")
            return True
        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 观看视频时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def try_like(self, probability: float):
        """尝试点赞当前视频"""
        try:
            if not self.running or random.random() >= probability:
                return False

            selectors = self.xpath_config.get("selectors", {}).get("like_button", [])
            for selector in selectors:
                try:
                    like_button = self.page.locator(selector)
                    count = await like_button.count()
                    if count == 1:
                        await like_button.click(timeout=5000)
                        self.stats['likes_given'] += 1
                        logger.info(f"YouTube机器人 {self.browser_id} 完成点赞 #{self.stats['likes_given']}")
                        await self.random_delay(0.5, 1.5)
                        return True
                except Exception:
                    continue
            return False
        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 点赞时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def try_subscribe(self, probability: float):
        """尝试订阅当前频道"""
        try:
            if not self.running or random.random() >= probability:
                return False

            selectors = self.xpath_config.get("selectors", {}).get("subscribe_button", [])
            for selector in selectors:
                try:
                    sub_button = self.page.locator(selector)
                    count = await sub_button.count()
                    if count == 1:
                        await sub_button.click(timeout=5000)
                        self.stats['subscriptions_made'] += 1
                        logger.info(f"YouTube机器人 {self.browser_id} 完成订阅 #{self.stats['subscriptions_made']}")
                        await self.random_delay(0.5, 1.5)
                        return True
                except Exception:
                    continue
            return False
        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 订阅时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def scroll_to_next(self):
        """滚动到下一个视频"""
        try:
            if not self.running:
                return False
            await self.page.keyboard.press('ArrowDown')
            await self.random_delay(1, 2)
            return True
        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 滚动时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    def stop(self):
        """停止机器人运行"""
        self.running = False
        logger.info(f"YouTube机器人 {self.browser_id} 收到停止指令")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page and self.context and len(self.context.pages) > 1:
                try:
                    await self.page.close()
                except Exception:
                    pass
            logger.info(f"YouTube机器人 {self.browser_id} 资源清理完成")
        except Exception as e:
            logger.error(f"YouTube机器人 {self.browser_id} 清理资源时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计数据"""
        return self.stats.copy()
