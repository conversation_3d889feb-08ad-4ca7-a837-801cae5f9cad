"""
YouTube自动化机器人

负责自动播放YouTube Shorts视频，模拟用户观看行为。

功能特点：
- 自动搜索和播放YouTube Shorts
- 模拟真实用户行为（滚动、点赞、评论等）
- 可配置的观看时长和行为模式
- 支持多种搜索关键词
"""

import time
import random
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class YouTubeBot:
    """YouTube自动化机器人"""
    
    def __init__(self, profile_id: str, config_file: str = "data/bot_configs/youtube_xpath.json"):
        """
        初始化YouTube机器人
        
        Args:
            profile_id: 浏览器配置文件ID
            config_file: 配置文件路径
        """
        self.profile_id = profile_id
        self.config_file = Path(config_file)
        self.driver = None
        self.config = self._load_config()
        
        # 运行状态
        self.is_running = False
        self.videos_watched = 0
        self.start_time = None
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        default_config = {
            "search_keywords": [
                "funny shorts",
                "trending shorts", 
                "viral videos",
                "entertainment"
            ],
            "watch_duration": {
                "min": 10,  # 最少观看秒数
                "max": 30   # 最多观看秒数
            },
            "actions": {
                "like_probability": 0.1,      # 点赞概率
                "comment_probability": 0.05,  # 评论概率
                "subscribe_probability": 0.02 # 订阅概率
            },
            "xpath_selectors": {
                "search_box": "//input[@id='search']",
                "search_button": "//button[@id='search-icon-legacy']",
                "shorts_video": "//a[contains(@href, '/shorts/')]",
                "like_button": "//button[@aria-label='赞']",
                "subscribe_button": "//button[contains(@aria-label, '订阅')]",
                "next_video": "//button[@aria-label='下一个视频']"
            },
            "delays": {
                "page_load": 3,
                "action_delay": 2,
                "scroll_delay": 1
            }
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并用户配置和默认配置
                    default_config.update(user_config)
                    logger.info(f"已加载配置文件: {self.config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                # 创建默认配置文件
                self._save_default_config(default_config)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
        
        return default_config
    
    def _save_default_config(self, config: Dict):
        """保存默认配置文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"已创建默认配置文件: {self.config_file}")
        except Exception as e:
            logger.error(f"保存默认配置文件失败: {e}")
    
    def start(self, duration_minutes: int = 60):
        """
        启动YouTube机器人
        
        Args:
            duration_minutes: 运行时长（分钟）
        """
        logger.info(f"启动YouTube机器人 - 配置文件ID: {self.profile_id}")
        
        try:
            # 初始化浏览器
            self._init_browser()
            
            # 设置运行状态
            self.is_running = True
            self.start_time = time.time()
            end_time = self.start_time + (duration_minutes * 60)
            
            # 访问YouTube
            self.driver.get("https://www.youtube.com")
            time.sleep(self.config["delays"]["page_load"])
            
            # 主循环
            while self.is_running and time.time() < end_time:
                try:
                    # 搜索Shorts视频
                    self._search_shorts()
                    
                    # 观看视频
                    self._watch_videos()
                    
                    # 随机休息
                    time.sleep(random.randint(5, 15))
                    
                except Exception as e:
                    logger.error(f"执行循环时发生错误: {e}")
                    time.sleep(10)  # 错误后等待10秒
            
            logger.info(f"YouTube机器人运行结束 - 共观看 {self.videos_watched} 个视频")
            
        except Exception as e:
            logger.error(f"YouTube机器人运行失败: {e}")
        finally:
            self.stop()
    
    def _init_browser(self):
        """初始化浏览器"""
        # 这里需要根据IXBrowser的具体API来实现
        # 暂时使用标准的Chrome WebDriver作为示例
        
        options = webdriver.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 注意：实际使用时需要替换为IXBrowser的连接方式
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("浏览器初始化完成")
    
    def _search_shorts(self):
        """搜索Shorts视频"""
        try:
            # 随机选择搜索关键词
            keyword = random.choice(self.config["search_keywords"])
            
            # 查找搜索框
            search_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, self.config["xpath_selectors"]["search_box"]))
            )
            
            # 清空并输入搜索词
            search_box.clear()
            search_box.send_keys(f"{keyword} shorts")
            
            # 点击搜索按钮
            search_button = self.driver.find_element(By.XPATH, self.config["xpath_selectors"]["search_button"])
            search_button.click()
            
            time.sleep(self.config["delays"]["page_load"])
            logger.info(f"搜索关键词: {keyword}")
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
    
    def _watch_videos(self):
        """观看视频"""
        try:
            # 查找Shorts视频链接
            video_links = self.driver.find_elements(By.XPATH, self.config["xpath_selectors"]["shorts_video"])
            
            if not video_links:
                logger.warning("未找到Shorts视频")
                return
            
            # 随机选择一个视频
            video_link = random.choice(video_links[:5])  # 从前5个中选择
            video_link.click()
            
            time.sleep(self.config["delays"]["page_load"])
            
            # 观看视频
            watch_duration = random.randint(
                self.config["watch_duration"]["min"],
                self.config["watch_duration"]["max"]
            )
            
            logger.info(f"开始观看视频，时长: {watch_duration}秒")
            
            # 在观看期间执行随机动作
            self._perform_random_actions(watch_duration)
            
            self.videos_watched += 1
            
        except Exception as e:
            logger.error(f"观看视频失败: {e}")
    
    def _perform_random_actions(self, duration: int):
        """在观看期间执行随机动作"""
        end_time = time.time() + duration
        
        while time.time() < end_time and self.is_running:
            try:
                # 随机滚动
                if random.random() < 0.3:
                    self.driver.execute_script("window.scrollBy(0, 100);")
                
                # 随机点赞
                if random.random() < self.config["actions"]["like_probability"]:
                    self._try_like()
                
                # 随机订阅
                if random.random() < self.config["actions"]["subscribe_probability"]:
                    self._try_subscribe()
                
                time.sleep(self.config["delays"]["action_delay"])
                
            except Exception as e:
                logger.debug(f"执行随机动作时出错: {e}")
    
    def _try_like(self):
        """尝试点赞"""
        try:
            like_button = self.driver.find_element(By.XPATH, self.config["xpath_selectors"]["like_button"])
            if like_button.is_enabled():
                like_button.click()
                logger.info("已点赞")
        except:
            pass  # 忽略点赞失败
    
    def _try_subscribe(self):
        """尝试订阅"""
        try:
            subscribe_button = self.driver.find_element(By.XPATH, self.config["xpath_selectors"]["subscribe_button"])
            if subscribe_button.is_enabled() and "订阅" in subscribe_button.text:
                subscribe_button.click()
                logger.info("已订阅")
        except:
            pass  # 忽略订阅失败
    
    def stop(self):
        """停止机器人"""
        self.is_running = False
        
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器已关闭")
            except:
                pass
        
        logger.info("YouTube机器人已停止")
    
    def get_status(self) -> Dict:
        """获取运行状态"""
        runtime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "is_running": self.is_running,
            "profile_id": self.profile_id,
            "videos_watched": self.videos_watched,
            "runtime_seconds": int(runtime),
            "runtime_formatted": f"{int(runtime//60)}分{int(runtime%60)}秒"
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试机器人
    bot = YouTubeBot("test_profile")
    
    try:
        # 运行5分钟测试
        bot.start(duration_minutes=5)
    except KeyboardInterrupt:
        print("用户中断")
        bot.stop()
