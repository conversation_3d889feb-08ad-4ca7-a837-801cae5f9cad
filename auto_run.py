#!/usr/bin/env python3
"""
IXBrowser自动运行脚本

自动读取配置文件，执行所有配置的浏览器自动化流程
完全自动化，无需手动参数

使用方法:
    python auto_run.py                    # 自动运行所有配置的浏览器（默认）
    python auto_run.py --random           # 随机运行一个浏览器
    python auto_run.py --count 3          # 随机运行3个浏览器
    python auto_run.py --profile 1267     # 运行指定的浏览器
"""

import sys
import asyncio
import random
import logging
import argparse
import time
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入模块
try:
    from app.browser_manager import BrowserManager, ConfigManager
    from bots.youtube_bot import YouTubeBot
    from bots.docker_bot import DockerBot
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AutoRunner:
    """自动运行器"""
    
    def __init__(self):
        """初始化自动运行器"""
        self.browser_manager = BrowserManager()
        self.config_manager = ConfigManager()
        self.running_bots = {}  # {profile_id: bot_instance}
    
    async def run_single_browser(self, profile_id: str, flow_id: str) -> bool:
        """
        运行单个浏览器的自动化流程
        
        Args:
            profile_id: 浏览器配置文件ID
            flow_id: 流程ID
            
        Returns:
            是否运行成功
        """
        try:
            logger.info(f"开始运行浏览器 {profile_id}，流程: {flow_id}")
            
            # 跳过"无"流程
            if flow_id == "none":
                logger.info(f"浏览器 {profile_id} 配置为无流程，跳过")
                return True
            
            # 启动浏览器
            ws_endpoint = self.browser_manager.start_browser(profile_id)
            if not ws_endpoint:
                logger.error(f"启动浏览器 {profile_id} 失败")
                return False
            
            # 等待浏览器完全启动
            await asyncio.sleep(3)
            
            # 获取流程类型和设置
            flow_type = self.config_manager.get_flow_type(flow_id)
            flow_settings = self.config_manager.load_flow_settings(flow_id)
            
            logger.info(f"浏览器 {profile_id} 流程类型: {flow_type}")
            
            # 创建对应的机器人
            bot = None
            if flow_type == "youtube":
                bot = YouTubeBot(profile_id, ws_endpoint)
            elif flow_type == "docker":
                bot = DockerBot(profile_id, ws_endpoint)
            else:
                logger.warning(f"未知的流程类型: {flow_type}")
                return False
            
            # 初始化机器人
            if not await bot.init_browser():
                logger.error(f"初始化机器人失败: {profile_id}")
                return False
            
            # 运行机器人
            self.running_bots[profile_id] = bot
            stats = await bot.run(flow_settings)
            
            # 清理
            await bot.cleanup()
            if profile_id in self.running_bots:
                del self.running_bots[profile_id]
            
            logger.info(f"浏览器 {profile_id} 运行完成，统计: {stats}")
            return True
            
        except Exception as e:
            logger.error(f"运行浏览器 {profile_id} 时发生错误: {e}")
            
            # 清理
            if profile_id in self.running_bots:
                try:
                    await self.running_bots[profile_id].cleanup()
                    del self.running_bots[profile_id]
                except:
                    pass
            
            return False
    
    async def run_random_browser(self) -> bool:
        """
        随机运行一个配置的浏览器
        
        Returns:
            是否运行成功
        """
        try:
            # 加载配置
            browser_configs = self.config_manager.load_browser_configs()
            if not browser_configs:
                logger.error("没有找到浏览器配置")
                return False
            
            # 过滤掉"无"流程的浏览器
            active_configs = {k: v for k, v in browser_configs.items() if v != "none"}
            if not active_configs:
                logger.error("没有找到配置了自动化流程的浏览器")
                return False
            
            # 随机选择一个浏览器
            profile_id = random.choice(list(active_configs.keys()))
            flow_id = active_configs[profile_id]
            
            logger.info(f"随机选择浏览器 {profile_id}，流程: {flow_id}")
            
            # 运行
            return await self.run_single_browser(profile_id, flow_id)
            
        except Exception as e:
            logger.error(f"随机运行浏览器时发生错误: {e}")
            return False
    
    async def run_all_browsers(self) -> Dict[str, bool]:
        """
        运行所有配置的浏览器
        
        Returns:
            运行结果字典 {profile_id: success}
        """
        try:
            # 加载配置
            browser_configs = self.config_manager.load_browser_configs()
            if not browser_configs:
                logger.error("没有找到浏览器配置")
                return {}
            
            results = {}
            
            # 逐个运行浏览器
            for profile_id, flow_id in browser_configs.items():
                if flow_id == "none":
                    logger.info(f"跳过浏览器 {profile_id}（无流程）")
                    results[profile_id] = True
                    continue
                
                logger.info(f"运行浏览器 {profile_id} ({len(results)+1}/{len(browser_configs)})")
                
                success = await self.run_single_browser(profile_id, flow_id)
                results[profile_id] = success
                
                # 浏览器间等待时间
                if profile_id != list(browser_configs.keys())[-1]:  # 不是最后一个
                    wait_time = random.randint(5, 15)
                    logger.info(f"等待 {wait_time} 秒后运行下一个浏览器")
                    await asyncio.sleep(wait_time)
            
            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            logger.info(f"批量运行完成: {success_count}/{total_count} 成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量运行浏览器时发生错误: {e}")
            return {}
    
    async def run_multiple_random(self, count: int) -> Dict[str, bool]:
        """
        随机运行多个浏览器
        
        Args:
            count: 运行数量
            
        Returns:
            运行结果字典
        """
        try:
            # 加载配置
            browser_configs = self.config_manager.load_browser_configs()
            active_configs = {k: v for k, v in browser_configs.items() if v != "none"}
            
            if not active_configs:
                logger.error("没有找到配置了自动化流程的浏览器")
                return {}
            
            # 随机选择指定数量的浏览器
            available_browsers = list(active_configs.keys())
            selected_count = min(count, len(available_browsers))
            selected_browsers = random.sample(available_browsers, selected_count)
            
            logger.info(f"随机选择了 {selected_count} 个浏览器: {selected_browsers}")
            
            results = {}
            
            # 逐个运行
            for i, profile_id in enumerate(selected_browsers):
                flow_id = active_configs[profile_id]
                logger.info(f"运行浏览器 {profile_id} ({i+1}/{selected_count})")
                
                success = await self.run_single_browser(profile_id, flow_id)
                results[profile_id] = success
                
                # 浏览器间等待时间
                if i < selected_count - 1:  # 不是最后一个
                    wait_time = random.randint(5, 15)
                    logger.info(f"等待 {wait_time} 秒后运行下一个浏览器")
                    await asyncio.sleep(wait_time)
            
            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"随机运行完成: {success_count}/{selected_count} 成功")
            
            return results
            
        except Exception as e:
            logger.error(f"随机运行多个浏览器时发生错误: {e}")
            return {}


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='IXBrowser自动运行脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                    # 自动运行所有配置的浏览器（默认）
  %(prog)s --random           # 随机运行一个浏览器
  %(prog)s --count 3          # 随机运行3个浏览器
  %(prog)s --profile 1267     # 运行指定的浏览器
        """
    )

    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        '--random',
        action='store_true',
        help='随机运行一个浏览器'
    )
    group.add_argument(
        '--count',
        type=int,
        help='随机运行指定数量的浏览器'
    )
    group.add_argument(
        '--profile',
        type=str,
        help='运行指定的浏览器配置文件ID'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    return parser.parse_args()


async def main():
    """主函数"""
    print("=" * 60)
    print("IXBrowser 自动运行脚本 v1.0.0")
    print("=" * 60)
    
    try:
        # 解析参数
        args = parse_arguments()
        
        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 创建自动运行器
        runner = AutoRunner()
        
        # 根据参数执行不同的运行模式
        if args.random:
            logger.info("开始随机运行一个浏览器")
            success = await runner.run_random_browser()
            results = {"random": success}
        elif args.count:
            logger.info(f"开始随机运行 {args.count} 个浏览器")
            results = await runner.run_multiple_random(args.count)
        elif args.profile:
            logger.info(f"开始运行指定浏览器: {args.profile}")
            # 需要获取流程ID
            config_manager = ConfigManager()
            browser_configs = config_manager.load_browser_configs()
            flow_id = browser_configs.get(args.profile, "none")
            if flow_id == "none":
                logger.error(f"浏览器 {args.profile} 未配置自动化流程")
                return
            success = await runner.run_single_browser(args.profile, flow_id)
            results = {args.profile: success}
        else:
            # 默认运行所有配置的浏览器
            logger.info("开始自动运行所有配置的浏览器")
            results = await runner.run_all_browsers()
        
        # 显示结果
        logger.info("=" * 40)
        logger.info("运行结果:")
        for profile_id, success in results.items():
            status = "成功" if success else "失败"
            logger.info(f"  浏览器 {profile_id}: {status}")
        logger.info("=" * 40)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
