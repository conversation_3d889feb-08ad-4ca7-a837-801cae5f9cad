"""
IXBrowser自动化机器人模块

此模块包含各种自动化机器人的实现：
- youtube_bot: YouTube自动化机器人
- docker_bot: Docker网站访问机器人

每个机器人都实现了标准的接口，可以通过统一的方式调用。

版本: 1.0
作者: AI Assistant
创建日期: 2025-07-16
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入机器人类
try:
    from .youtube_bot import YouTubeBot
    from .docker_bot import DockerBot
    
    __all__ = [
        "YouTubeBot",
        "DockerBot"
    ]
    
except ImportError as e:
    # 如果某些机器人模块不存在，只导入可用的
    print(f"警告: 部分机器人模块导入失败: {e}")
    __all__ = []


# 机器人注册表
AVAILABLE_BOTS = {
    "youtube_shorts_v1": {
        "name": "YouTube挂机机器人",
        "class": "YouTubeBot",
        "script": "run_youtube_bot.py",
        "description": "自动播放YouTube Shorts视频"
    },
    "docker_visit_v1": {
        "name": "Docker网站访问机器人", 
        "class": "DockerBot",
        "script": "run_docker_bot.py",
        "description": "访问指定网站列表"
    }
}


def get_bot_info(bot_id: str) -> dict:
    """
    获取机器人信息
    
    Args:
        bot_id: 机器人ID
        
    Returns:
        机器人信息字典
    """
    return AVAILABLE_BOTS.get(bot_id, {})


def list_available_bots() -> dict:
    """
    列出所有可用的机器人
    
    Returns:
        机器人信息字典
    """
    return AVAILABLE_BOTS.copy()


def create_bot_instance(bot_id: str, **kwargs):
    """
    创建机器人实例
    
    Args:
        bot_id: 机器人ID
        **kwargs: 机器人初始化参数
        
    Returns:
        机器人实例，如果创建失败返回None
    """
    bot_info = get_bot_info(bot_id)
    if not bot_info:
        return None
    
    class_name = bot_info.get("class")
    if class_name == "YouTubeBot" and "YouTubeBot" in globals():
        return YouTubeBot(**kwargs)
    elif class_name == "DockerBot" and "DockerBot" in globals():
        return DockerBot(**kwargs)
    else:
        return None
