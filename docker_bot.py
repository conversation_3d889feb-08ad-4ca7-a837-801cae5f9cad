#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker 自动化机器人
执行网站访问自动化任务
"""

import asyncio
import logging
import random
import time
from typing import Dict, Any, List, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

logger = logging.getLogger(__name__)


class DockerBot:
    """Docker 自动化机器人"""
    
    def __init__(self, browser_id: str, ws_endpoint: str):
        """
        初始化 Docker 机器人
        
        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
        """
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.running = False
        
        # 统计信息
        self.stats = {
            "sites_visited": 0,
            "successful_visits": 0,
            "failed_visits": 0,
            "total_wait_time": 0,
            "errors": 0
        }
    
    async def init_browser(self) -> bool:
        """
        初始化浏览器连接
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            logger.info(f"正在初始化 Docker 机器人浏览器连接: {self.browser_id}")

            # 启动 Playwright
            self.playwright = await async_playwright().start()

            # 连接到现有浏览器实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                self.ws_endpoint,
                timeout=30000
            )

            # 获取已有的context
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                # 创建新的context
                self.context = await self.browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )

            # 获取已有的页面，如果没有则创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]
                # 关闭其他页面
                for page in pages[1:]:
                    await page.close()
                logger.info(f"使用现有页面: {await self.page.title()}")
            else:
                self.page = await self.context.new_page()
                logger.info("创建新页面")

            # 设置页面超时时间
            self.page.set_default_timeout(30000)

            logger.info(f"Docker 机器人 {self.browser_id} 初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化 Docker 机器人 {self.browser_id} 失败: {e}")
            await self.cleanup()
            return False

    async def check_connection(self) -> bool:
        """检查浏览器连接是否仍然有效"""
        try:
            await self.page.evaluate("() => document.location.href")
            return True
        except Exception:
            return False

    async def visit_site(self, site_config: Dict[str, Any]) -> bool:
        """
        访问指定网站
        
        Args:
            site_config: 网站配置
            
        Returns:
            bool: 是否访问成功
        """
        url = site_config.get("url", "")
        name = site_config.get("name", url)
        wait_time_range = site_config.get("wait_time", [3, 5])
        
        try:
            logger.info(f"Docker 机器人 {self.browser_id} 正在访问: {name} ({url})")
            
            # 导航到网站
            await self.page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            # 等待页面加载
            await asyncio.sleep(1)
            
            # 随机等待时间
            wait_time = random.randint(wait_time_range[0], wait_time_range[1])
            logger.info(f"Docker 机器人 {self.browser_id} 在 {name} 等待 {wait_time} 秒")
            
            await asyncio.sleep(wait_time)
            
            # 更新统计
            self.stats["sites_visited"] += 1
            self.stats["successful_visits"] += 1
            self.stats["total_wait_time"] += wait_time
            
            logger.info(f"Docker 机器人 {self.browser_id} 成功访问 {name}")
            return True
            
        except Exception as e:
            logger.error(f"Docker 机器人 {self.browser_id} 访问 {name} 失败: {e}")
            self.stats["sites_visited"] += 1
            self.stats["failed_visits"] += 1
            self.stats["errors"] += 1
            return False

    async def close_current_tab(self):
        """关闭当前标签页"""
        try:
            # 如果有多个页面，关闭当前页面并切换到其他页面
            pages = self.context.pages
            if len(pages) > 1:
                await self.page.close()
                self.page = pages[0] if pages[0] != self.page else pages[1]
                logger.info(f"Docker 机器人 {self.browser_id} 关闭标签页并切换")
            else:
                # 如果只有一个页面，创建新页面后关闭旧页面
                new_page = await self.context.new_page()
                await self.page.close()
                self.page = new_page
                logger.info(f"Docker 机器人 {self.browser_id} 关闭标签页并创建新页面")
                
        except Exception as e:
            logger.error(f"Docker 机器人 {self.browser_id} 关闭标签页失败: {e}")

    async def run(self, settings: Dict[str, Any]) -> Dict[str, int]:
        """
        运行 Docker 自动化任务
        
        Args:
            settings: 运行设置
            
        Returns:
            Dict[str, int]: 运行统计数据
        """
        self.running = True
        
        try:
            logger.info(f"Docker 机器人 {self.browser_id} 开始运行")
            
            sites = settings.get("sites", [])
            close_tabs = settings.get("close_tabs_after_visit", True)
            between_sites_wait = settings.get("browser_wait_between_sites", [2, 4])
            
            if not sites:
                logger.warning(f"Docker 机器人 {self.browser_id} 没有配置访问网站")
                return self.stats.copy()
            
            logger.info(f"Docker 机器人 {self.browser_id} 将访问 {len(sites)} 个网站")
            
            for i, site_config in enumerate(sites):
                if not self.running:
                    logger.info(f"Docker 机器人 {self.browser_id} 收到停止信号")
                    break
                
                # 检查连接
                if not await self.check_connection():
                    logger.error(f"Docker 机器人 {self.browser_id} 浏览器连接已断开")
                    break
                
                # 访问网站
                success = await self.visit_site(site_config)
                
                # 如果设置了关闭标签页且访问成功
                if close_tabs and success and i < len(sites) - 1:
                    await self.close_current_tab()
                
                # 网站间等待时间
                if i < len(sites) - 1:
                    wait_time = random.randint(between_sites_wait[0], between_sites_wait[1])
                    logger.info(f"Docker 机器人 {self.browser_id} 等待 {wait_time} 秒后访问下一个网站")
                    await asyncio.sleep(wait_time)
            
            logger.info(f"Docker 机器人 {self.browser_id} 任务完成 - "
                       f"访问: {self.stats['sites_visited']}, "
                       f"成功: {self.stats['successful_visits']}, "
                       f"失败: {self.stats['failed_visits']}")

        except Exception as e:
            logger.error(f"Docker 机器人 {self.browser_id} 运行时发生错误: {e}")
            self.stats["errors"] += 1

        finally:
            self.running = False
            logger.info(f"Docker 机器人 {self.browser_id} 运行完成")

        return self.stats.copy()

    async def stop(self):
        """停止机器人"""
        self.running = False
        logger.info(f"Docker 机器人 {self.browser_id} 收到停止信号")

    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info(f"Docker 机器人 {self.browser_id} 资源清理完成")
        except Exception as e:
            logger.error(f"Docker 机器人 {self.browser_id} 清理资源时出错: {e}")

    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()
