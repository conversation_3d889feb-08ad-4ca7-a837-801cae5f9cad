# IXBrowser GUI配置工具依赖包
# 核心依赖
requests>=2.28.0          # HTTP客户端，用于调用IXBrowser API
urllib3>=1.26.0           # HTTP库，requests的依赖

# 浏览器自动化
playwright>=1.40.0        # 现代浏览器自动化库，替代Selenium
asyncio                   # 异步编程支持（Python 3.7+内置）

# GUI相关（tkinter是Python内置，无需安装）
# 如果需要更美观的界面，可以取消下面的注释
# PyQt5>=5.15.0
# PyQt6>=6.4.0

# 开发和调试工具（可选）
# pytest>=7.0.0           # 单元测试
# black>=22.0.0            # 代码格式化
# flake8>=5.0.0            # 代码检查
