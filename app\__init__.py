"""
IXBrowser GUI配置工具 - 应用程序核心模块

此模块包含GUI应用程序的核心组件：
- ui_manager: 用户界面管理
- api_handler: IXBrowser API调用处理  
- config_handler: 配置文件读写管理

版本: 1.0
作者: AI Assistant
创建日期: 2025-07-16
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入核心模块
from .ui_manager import UIManager
from .api_handler import APIHandler  
from .config_handler import ConfigHandler

__all__ = [
    "UIManager",
    "APIHandler", 
    "ConfigHandler"
]
