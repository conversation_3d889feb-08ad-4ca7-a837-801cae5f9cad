#!/usr/bin/env python3
"""
IXBrowser GUI配置工具 - 主程序入口

这是一个极简的GUI配置工具，用于管理IXBrowser指纹浏览器的自动化流程。
用户可以通过表格界面为每个浏览器配置文件指定自动化脚本。

功能特点：
- 直观的表格界面
- 表格内下拉菜单编辑
- 智能配置合并
- 数据持久化存储

使用方法：
1. 确保IXBrowser正在运行（默认端口53200）
2. 双击运行此文件或在命令行执行: python main.py
3. 点击"刷新"获取最新的浏览器列表
4. 双击"自动化流程"列的单元格选择流程
5. 点击"保存"保存配置

作者: AI Assistant
版本: 1.0.0
创建日期: 2025-07-16
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入应用程序模块
try:
    from app import UIManager, APIHandler, ConfigHandler
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的文件都存在于正确的位置")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ixbrowser_gui.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class IXBrowserGUIApp:
    """IXBrowser GUI应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        logger.info("正在初始化IXBrowser GUI应用程序...")
        
        # 初始化核心组件
        self.config_handler = ConfigHandler()
        self.api_handler = APIHandler()
        self.ui_manager = UIManager(
            on_refresh_callback=self.refresh_data,
            on_save_callback=self.save_config
        )
        
        # 应用程序状态
        self.is_initialized = False
        
    def initialize(self):
        """初始化应用程序数据"""
        try:
            logger.info("正在初始化应用程序数据...")
            
            # 创建主窗口
            self.ui_manager.create_main_window()
            
            # 加载初始数据
            self.refresh_data()
            
            self.is_initialized = True
            logger.info("应用程序初始化完成")
            
        except Exception as e:
            logger.error(f"应用程序初始化失败: {e}")
            self.ui_manager.show_error("初始化错误", f"应用程序初始化失败:\n{e}")
            return False
        
        return True
    
    def refresh_data(self):
        """刷新数据（从API和配置文件）"""
        try:
            logger.info("开始刷新数据...")
            
            # 更新状态
            self.ui_manager.update_status("正在获取浏览器列表...")
            
            # 获取浏览器列表
            profiles, api_success = self.api_handler.get_profiles()
            
            if not api_success:
                self.ui_manager.update_status("API调用失败，使用缓存数据")
                logger.warning("API调用失败，使用缓存或空数据")
            
            # 格式化浏览器数据
            formatted_profiles = []
            for profile in profiles:
                formatted = self.api_handler.format_profile_for_display(profile)
                formatted_profiles.append(formatted)
            
            # 加载流程定义
            self.ui_manager.update_status("正在加载流程定义...")
            flows = self.config_handler.load_flows()
            
            if not flows:
                logger.warning("未找到流程定义，请检查data/flows.json文件")
                self.ui_manager.show_error("配置错误", 
                    "未找到流程定义文件(data/flows.json)，请确保文件存在且格式正确")
                return
            
            # 加载现有配置
            self.ui_manager.update_status("正在加载现有配置...")
            existing_config = self.config_handler.load_config()
            
            # 智能合并配置
            merged_config = self.config_handler.merge_configs(
                formatted_profiles, existing_config
            )
            
            # 更新界面
            self.ui_manager.update_table_data(formatted_profiles, flows, merged_config)
            
            # 更新状态信息
            status_msg = f"已加载 {len(formatted_profiles)} 个浏览器"
            if not api_success:
                status_msg += " (离线模式)"
            
            self.ui_manager.update_status(status_msg)
            
            logger.info(f"数据刷新完成: {len(formatted_profiles)} 个浏览器, {len(flows)} 个流程")
            
        except Exception as e:
            logger.error(f"刷新数据时发生错误: {e}")
            self.ui_manager.update_status("刷新失败")
            self.ui_manager.show_error("刷新错误", f"刷新数据时发生错误:\n{e}")
    
    def save_config(self, config: dict) -> bool:
        """
        保存配置
        
        Args:
            config: 配置字典 {profile_id: flow_id}
            
        Returns:
            保存是否成功
        """
        try:
            logger.info(f"正在保存配置: {len(config)} 个项目")
            
            # 过滤掉值为空的配置项
            filtered_config = {k: v for k, v in config.items() if k and v}
            
            # 保存配置
            success = self.config_handler.save_config(filtered_config)
            
            if success:
                logger.info("配置保存成功")
            else:
                logger.error("配置保存失败")
            
            return success
            
        except Exception as e:
            logger.error(f"保存配置时发生错误: {e}")
            return False
    
    def run(self):
        """运行应用程序"""
        if not self.is_initialized:
            if not self.initialize():
                return
        
        logger.info("启动GUI主循环...")
        
        try:
            # 显示连接状态信息
            connection_status = self.api_handler.get_connection_status()
            if connection_status["connected"]:
                logger.info("IXBrowser API连接正常")
            else:
                logger.warning("无法连接到IXBrowser API，将使用离线模式")
                self.ui_manager.show_info("连接提醒", 
                    "无法连接到IXBrowser API (http://127.0.0.1:53200)\n"
                    "请确保IXBrowser正在运行，或者使用离线模式")
            
            # 运行GUI主循环
            self.ui_manager.run()
            
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"运行时发生未处理的错误: {e}")
            self.ui_manager.show_error("运行时错误", f"程序运行时发生错误:\n{e}")
        finally:
            logger.info("应用程序退出")


def main():
    """主函数"""
    print("=" * 50)
    print("IXBrowser GUI配置工具 v1.0.0")
    print("=" * 50)
    
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 检查必要的目录和文件
        required_dirs = ["app", "data", "bots"]
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                print(f"错误: 缺少必要的目录 '{dir_name}'")
                sys.exit(1)
        
        # 创建并运行应用程序
        app = IXBrowserGUIApp()
        app.run()
        
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
