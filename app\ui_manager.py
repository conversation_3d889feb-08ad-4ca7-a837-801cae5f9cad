"""
GUI界面管理模块

负责创建和管理主窗口界面，包括：
- 主窗口布局
- 浏览器信息表格
- 下拉菜单交互
- 按钮事件处理
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Any, Optional, Callable
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UIManager:
    """GUI界面管理器"""
    
    def __init__(self, 
                 on_refresh_callback: Optional[Callable] = None,
                 on_save_callback: Optional[Callable] = None):
        """
        初始化界面管理器
        
        Args:
            on_refresh_callback: 刷新按钮回调函数
            on_save_callback: 保存按钮回调函数
        """
        self.on_refresh_callback = on_refresh_callback
        self.on_save_callback = on_save_callback
        
        # 界面组件
        self.root = None
        self.tree = None
        self.status_label = None
        self.refresh_btn = None
        self.save_btn = None
        
        # 数据存储
        self.profiles_data = []  # 浏览器配置文件数据
        self.flows_data = []     # 流程定义数据
        self.current_config = {} # 当前配置状态
        
        # 下拉菜单相关
        self.combo_vars = {}     # 存储每行的下拉菜单变量
        self.active_combo = None # 当前活动的下拉菜单
        
    def create_main_window(self) -> tk.Tk:
        """
        创建主窗口
        
        Returns:
            主窗口对象
        """
        self.root = tk.Tk()
        self.root.title("IXBrowser 自动化配置工具")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建标题
        title_label = ttk.Label(main_frame, 
                               text="IXBrowser 自动化流程配置", 
                               font=("微软雅黑", 14, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 创建表格框架
        self._create_table_frame(main_frame)
        
        # 创建按钮框架
        self._create_button_frame(main_frame)
        
        # 创建状态栏
        self._create_status_bar(main_frame)
        
        return self.root
    
    def _create_table_frame(self, parent):
        """创建表格框架"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview表格
        columns = ("profile_id", "name", "group_name", "flow")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.tree.heading("profile_id", text="窗口序号")
        self.tree.heading("name", text="窗口名称") 
        self.tree.heading("group_name", text="分组名称")
        self.tree.heading("flow", text="自动化流程")
        
        # 设置列宽
        self.tree.column("profile_id", width=100, minwidth=80)
        self.tree.column("name", width=200, minwidth=150)
        self.tree.column("group_name", width=150, minwidth=100)
        self.tree.column("flow", width=200, minwidth=150)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局表格和滚动条
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 绑定双击事件用于编辑流程
        self.tree.bind("<Double-1>", self._on_cell_double_click)
        self.tree.bind("<Button-1>", self._on_cell_click)
    
    def _create_button_frame(self, parent):
        """创建按钮框架"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, pady=(0, 10))
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(button_frame, 
                                     text="刷新", 
                                     command=self._on_refresh_click,
                                     width=12)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存按钮
        self.save_btn = ttk.Button(button_frame, 
                                  text="保存", 
                                  command=self._on_save_click,
                                  width=12)
        self.save_btn.pack(side=tk.LEFT)
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, 
                                     text="就绪", 
                                     relief=tk.SUNKEN,
                                     anchor=tk.W)
        self.status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    def update_table_data(self, profiles: List[Dict], flows: List[Dict], config: Dict[str, str]):
        """
        更新表格数据
        
        Args:
            profiles: 浏览器配置文件列表
            flows: 流程定义列表  
            config: 当前配置映射
        """
        # 保存数据
        self.profiles_data = profiles
        self.flows_data = flows
        self.current_config = config.copy()
        
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.combo_vars.clear()
        
        # 创建流程选项列表
        flow_options = [(flow["flow_name"], flow["flow_id"]) for flow in flows]
        
        # 添加新数据
        for profile in profiles:
            profile_id = str(profile.get("profile_id", ""))
            name = profile.get("name", "未命名")
            group_name = profile.get("group_name", "默认分组")
            
            # 获取当前配置的流程
            current_flow_id = config.get(profile_id, "none")
            current_flow_name = self._get_flow_name_by_id(current_flow_id)
            
            # 插入行数据
            item_id = self.tree.insert("", tk.END, values=(
                profile_id, name, group_name, current_flow_name
            ))
            
            # 为每行创建下拉菜单变量
            var = tk.StringVar(value=current_flow_id)
            self.combo_vars[item_id] = {
                "var": var,
                "profile_id": profile_id,
                "options": flow_options
            }
        
        # 更新状态
        self.update_status(f"已加载 {len(profiles)} 个浏览器配置")
        logger.info(f"表格数据已更新: {len(profiles)} 个配置文件")
    
    def _get_flow_name_by_id(self, flow_id: str) -> str:
        """根据flow_id获取流程名称"""
        for flow in self.flows_data:
            if flow.get("flow_id") == flow_id:
                return flow.get("flow_name", flow_id)
        return "未知流程"
    
    def _on_cell_click(self, event):
        """处理单元格点击事件"""
        # 隐藏之前的下拉菜单
        if self.active_combo:
            self.active_combo.destroy()
            self.active_combo = None
    
    def _on_cell_double_click(self, event):
        """处理单元格双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return
        
        # 获取点击的列
        column = self.tree.identify_column(event.x)
        
        # 只有"自动化流程"列可以编辑（第4列）
        if column != "#4":
            return
        
        # 获取单元格位置
        bbox = self.tree.bbox(item, column)
        if not bbox:
            return
        
        # 隐藏之前的下拉菜单
        if self.active_combo:
            self.active_combo.destroy()
        
        # 创建下拉菜单
        combo_data = self.combo_vars.get(item)
        if not combo_data:
            return
        
        # 创建Combobox
        self.active_combo = ttk.Combobox(
            self.tree,
            textvariable=combo_data["var"],
            values=[name for name, _ in combo_data["options"]],
            state="readonly"
        )
        
        # 设置位置和大小
        x, y, width, height = bbox
        self.active_combo.place(x=x, y=y, width=width, height=height)
        
        # 绑定事件
        self.active_combo.bind("<<ComboboxSelected>>", 
                              lambda e: self._on_combo_selected(item))
        self.active_combo.bind("<FocusOut>", 
                              lambda e: self._hide_combo())
        self.active_combo.bind("<Escape>", 
                              lambda e: self._hide_combo())
        
        # 获得焦点
        self.active_combo.focus()
    
    def _on_combo_selected(self, item_id):
        """处理下拉菜单选择事件"""
        combo_data = self.combo_vars.get(item_id)
        if not combo_data:
            return
        
        # 获取选中的流程名称和ID
        selected_name = combo_data["var"].get()
        selected_id = None
        
        for name, flow_id in combo_data["options"]:
            if name == selected_name:
                selected_id = flow_id
                break
        
        if selected_id:
            # 更新表格显示
            current_values = list(self.tree.item(item_id, "values"))
            current_values[3] = selected_name  # 更新流程列
            self.tree.item(item_id, values=current_values)
            
            # 更新内部配置状态
            profile_id = combo_data["profile_id"]
            self.current_config[profile_id] = selected_id
            
            logger.info(f"浏览器 {profile_id} 的流程已更改为: {selected_name}")
        
        # 隐藏下拉菜单
        self._hide_combo()
    
    def _hide_combo(self):
        """隐藏下拉菜单"""
        if self.active_combo:
            self.active_combo.destroy()
            self.active_combo = None
    
    def _on_refresh_click(self):
        """处理刷新按钮点击"""
        self.update_status("正在刷新...")
        self.refresh_btn.config(state="disabled")
        
        # 调用回调函数
        if self.on_refresh_callback:
            self.root.after(100, self._execute_refresh)  # 延迟执行以更新UI
    
    def _execute_refresh(self):
        """执行刷新操作"""
        try:
            self.on_refresh_callback()
        except Exception as e:
            logger.error(f"刷新操作失败: {e}")
            messagebox.showerror("错误", f"刷新失败: {e}")
        finally:
            self.refresh_btn.config(state="normal")
    
    def _on_save_click(self):
        """处理保存按钮点击"""
        self.update_status("正在保存...")
        self.save_btn.config(state="disabled")
        
        # 调用回调函数
        if self.on_save_callback:
            self.root.after(100, self._execute_save)  # 延迟执行以更新UI
    
    def _execute_save(self):
        """执行保存操作"""
        try:
            success = self.on_save_callback(self.current_config)
            if success:
                self.update_status("保存成功！")
                messagebox.showinfo("成功", "配置已成功保存！")
            else:
                self.update_status("保存失败")
                messagebox.showerror("错误", "配置保存失败，请检查文件权限")
        except Exception as e:
            logger.error(f"保存操作失败: {e}")
            self.update_status("保存失败")
            messagebox.showerror("错误", f"保存失败: {e}")
        finally:
            self.save_btn.config(state="normal")
    
    def update_status(self, message: str):
        """更新状态栏"""
        if self.status_label:
            self.status_label.config(text=message)
            self.root.update_idletasks()
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        messagebox.showerror(title, message)
    
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        messagebox.showinfo(title, message)
    
    def run(self):
        """运行GUI主循环"""
        if self.root:
            self.root.mainloop()


# 测试代码
if __name__ == "__main__":
    def test_refresh():
        print("刷新回调被调用")
    
    def test_save(config):
        print(f"保存回调被调用: {config}")
        return True
    
    # 创建测试界面
    ui = UIManager(on_refresh_callback=test_refresh, on_save_callback=test_save)
    root = ui.create_main_window()
    
    # 添加测试数据
    test_profiles = [
        {"profile_id": "1001", "name": "测试浏览器1", "group_name": "测试组"},
        {"profile_id": "1002", "name": "测试浏览器2", "group_name": "测试组"}
    ]
    test_flows = [
        {"flow_id": "youtube_shorts_v1", "flow_name": "YouTube挂机"},
        {"flow_id": "none", "flow_name": "无"}
    ]
    test_config = {"1001": "youtube_shorts_v1", "1002": "none"}
    
    ui.update_table_data(test_profiles, test_flows, test_config)
    
    ui.run()
